namespace ShiningCMusicApp.Models
{
    // Local configuration model for appsettings.json
    public class LocalAppSettings
    {
        public string ApiBaseUrl { get; set; } = string.Empty;
        public string SyncfusionLicense { get; set; } = string.Empty;
        public SessionTimeoutSettings SessionTimeout { get; set; } = new();
    }

    public class SessionTimeoutSettings
    {
        public int TimeoutMinutes { get; set; } = 30;
    }
}
